import { createContext, useContext, useEffect, useState, useMemo, useCallback } from 'react';
import type { ReactNode } from 'react';
import type { Session, User } from '@supabase/supabase-js';
import { supabase, getAuthRedirectUrl } from '../lib/supabase';

type AuthContextType = {
  session: Session | null;
  user: User | null;
  loading: boolean;
  isAnonymous: boolean;
  signIn: (email: string, password: string) => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null } | null;
  }>;
  signUp: (email: string, password: string) => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null } | null;
  }>;
  signInAnonymously: () => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null } | null;
  }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{
    error: Error | null;
    data: any;
  }>;
  updatePassword: (newPassword: string) => Promise<{
    error: Error | null;
    data: { user: User | null } | null;
  }>;
  linkIdentity: (email: string, password: string) => Promise<{
    error: Error | null;
    data: { user: User | null } | null;
  }>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAnonymous, setIsAnonymous] = useState(false);

  // Reduce console logging to only essential state changes
  useEffect(() => {
    // Only log when there are meaningful state changes, not on every render
    if (!loading) {
      console.log('🔐 AuthContext state stabilized:', {
        hasSession: !!session,
        hasUser: !!user,
        isAnonymous,
        userId: user?.id
      });
    }
  }, [session?.access_token, user?.id, loading, isAnonymous]); // More specific dependencies

  useEffect(() => {
    let mounted = true;

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (!mounted) return;

      setSession(session);
      setUser(session?.user ?? null);
      setIsAnonymous(session?.user?.is_anonymous ?? false);
      setLoading(false);
    });

    // Listen for auth changes with debouncing to prevent excessive updates
    let timeoutId: NodeJS.Timeout;
    const { data } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        if (!mounted) return;

        // Clear any pending updates
        if (timeoutId) clearTimeout(timeoutId);

        // Debounce rapid auth state changes (common on window focus)
        // Increased timeout to 250ms to properly coalesce rapid auth state changes
        // and prevent excessive re-renders when switching browser tabs
        timeoutId = setTimeout(() => {
          if (!mounted) return;

          setSession(session);
          setUser(session?.user ?? null);
          setIsAnonymous(session?.user?.is_anonymous ?? false);
          setLoading(false);
        }, 250); // 250ms debounce - optimized for tab switching performance
      }
    );

    return () => {
      mounted = false;
      if (timeoutId) clearTimeout(timeoutId);
      data?.subscription.unsubscribe();
    };
  }, []);

  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true);
    try {
      const result = await supabase.auth.signInWithPassword({ email, password });

      // If authentication failed, reset loading state immediately
      if (result.error) {
        setLoading(false);
      }
      // If successful, onAuthStateChange will handle setting loading to false

      return result;
    } catch (error) {
      // Handle any unexpected errors
      setLoading(false);
      throw error;
    }
  }, []);
  
  const signUp = useCallback(async (email: string, password: string) => {
    setLoading(true);
    try {
      const result = await supabase.auth.signUp({ email, password });

      if (result.error) {
        setLoading(false);
      }

      return result;
    } catch (error) {
      setLoading(false);
      throw error;
    }
  }, []);

  const signInAnonymously = useCallback(async () => {
    setLoading(true);
    try {
      const result = await supabase.auth.signInAnonymously();

      if (result.error) {
        setLoading(false);
      }

      return result;
    } catch (error) {
      setLoading(false);
      throw error;
    }
  }, []);

  const signOut = useCallback(async () => {
    setLoading(true);
    try {
      await supabase.auth.signOut();
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  }, []);

  const resetPassword = useCallback(async (email: string) => {
    setLoading(true);
    try {
      const redirectTo = `${getAuthRedirectUrl()}reset-password`;
      return await supabase.auth.resetPasswordForEmail(email, {
        redirectTo,
      });
    } finally {
      setLoading(false); // We set loading to false here since this doesn't trigger onAuthStateChange
    }
  }, []);

  const updatePassword = useCallback(async (newPassword: string) => {
    setLoading(true);
    try {
      return await supabase.auth.updateUser({
        password: newPassword,
      });
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  }, []);

  const linkIdentity = useCallback(async (email: string, password: string) => {
    setLoading(true);
    try {
      // First update the user with email and password
      return await supabase.auth.updateUser({
        email,
        password,
      });
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    session,
    user,
    loading,
    isAnonymous,
    signIn,
    signUp,
    signInAnonymously,
    signOut,
    resetPassword,
    updatePassword,
    linkIdentity,
  }), [session, user, loading, isAnonymous, signIn, signUp, signInAnonymously, signOut, resetPassword, updatePassword, linkIdentity]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
